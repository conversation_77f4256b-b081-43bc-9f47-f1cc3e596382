import pytest
from unittest.mock import patch, AsyncMock
from parlant.core.loggers import LogLevel
from app.main import main
from app.env import load_env

@pytest.mark.asyncio
async def test_main_creates_agent():
    with patch('app.main.p.Server') as mock_server, \
         patch('app.main.os.getenv') as mock_getenv, \
         patch('app.env.load_env') as mock_load_env:

        # Mock environment variables
        def mock_getenv_side_effect(key):
            if key == "APP_PORT":
                return "8800"
            elif key == "DATABASE_URL":
                return "mongodb://test:27017/"
            elif key == "LOG_LEVEL":
                return "INFO"
            return None

        mock_getenv.side_effect = mock_getenv_side_effect
        mock_load_env.return_value = None

        # Mock server instance
        mock_server_instance = AsyncMock()
        mock_server.return_value.__aenter__.return_value = mock_server_instance

        await main()

        # Verify load_env was called
        mock_load_env.assert_called_once()

        # Verify server was created with correct parameters
        mock_server.assert_called_once_with(
            session_store="mongodb://test:27017/",
            customer_store="mongodb://test:27017/",
            port=8800,
            log_level=LogLevel.INFO
        )

        # Verify agent was created with correct parameters
        mock_server_instance.create_agent.assert_called_once_with(
            name="Son-Vidéo IA",
            description="Tu es un conseiller Son-Vidéo."
        )


@pytest.mark.asyncio
async def test_main_with_different_port():
    """Test that main() uses the correct port from environment"""
    with patch('app.main.p.Server') as mock_server, \
         patch('app.main.os.getenv') as mock_getenv, \
         patch('app.env.load_env') as mock_load_env:

        # Mock environment variables with different port
        def mock_getenv_side_effect(key):
            if key == "APP_PORT":
                return "9000"
            elif key == "DATABASE_URL":
                return "mongodb://test:27017/"
            elif key == "LOG_LEVEL":
                return "DEBUG"
            return None

        mock_getenv.side_effect = mock_getenv_side_effect
        mock_load_env.return_value = None

        # Mock server instance
        mock_server_instance = AsyncMock()
        mock_server.return_value.__aenter__.return_value = mock_server_instance

        await main()

        # Verify server was created with the correct parameters
        mock_server.assert_called_once_with(
            session_store="mongodb://test:27017/",
            customer_store="mongodb://test:27017/",
            port=9000,
            log_level=LogLevel.DEBUG
        )


@pytest.mark.asyncio
async def test_main_with_default_log_level():
    """Test that main() uses default log level when LOG_LEVEL is not set"""
    with patch('app.main.p.Server') as mock_server, \
         patch('app.main.os.getenv') as mock_getenv, \
         patch('app.env.load_env') as mock_load_env:

        # Mock environment variables without LOG_LEVEL
        def mock_getenv_side_effect(key):
            if key == "APP_PORT":
                return "8800"
            elif key == "DATABASE_URL":
                return "mongodb://test:27017/"
            elif key == "LOG_LEVEL":
                return None  # Not set, should default to "INFO"
            return None

        mock_getenv.side_effect = mock_getenv_side_effect
        mock_load_env.return_value = None

        # Mock server instance
        mock_server_instance = AsyncMock()
        mock_server.return_value.__aenter__.return_value = mock_server_instance

        await main()

        # Verify server was created with default log level
        mock_server.assert_called_once_with(
            session_store="mongodb://test:27017/",
            customer_store="mongodb://test:27017/",
            port=8800,
            log_level=LogLevel.INFO
        )


@pytest.mark.asyncio
async def test_load_env_dev():
    """Test load_env function with dev environment"""
    with patch('app.env.os.getenv') as mock_getenv, \
         patch('app.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "dev"

        await load_env()

        # Verify correct .env files are loaded for dev
        assert mock_load_dotenv.call_count == 2
        mock_load_dotenv.assert_any_call(".env.local")
        mock_load_dotenv.assert_any_call(".env")


@pytest.mark.asyncio
async def test_load_env_test():
    """Test load_env function with test environment"""
    with patch('app.env.os.getenv') as mock_getenv, \
         patch('app.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "test"

        await load_env()

        # Verify correct .env file is loaded for test
        mock_load_dotenv.assert_called_once_with(".env.test")


@pytest.mark.asyncio
async def test_load_env_production():
    """Test load_env function with production environment"""
    with patch('app.env.os.getenv') as mock_getenv, \
         patch('app.env.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "prod"

        await load_env()

        # Verify default .env file is loaded for production
        mock_load_dotenv.assert_called_once_with(".env")