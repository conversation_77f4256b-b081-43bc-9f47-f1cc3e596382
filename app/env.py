import os
from lagom.environment import Env

from dotenv import load_dotenv


class GlobalEnv(Env):
    app_env: str
    app_port: int
    database_url: str
    log_level: str

async def load_env():
  env = os.getenv("APP_ENV") or "dev"
  print(f"Loading env {env}")

  if env == "dev":
    load_dotenv(".env.local")
    load_dotenv(".env")
    return
  if env == "test":
    load_dotenv(".env.test")
    return

  load_dotenv(".env")
