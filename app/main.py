import asyncio
import os
import parlant.sdk as p
from dotenv import load_dotenv
from parlant.core.loggers import LogLevel

from app.env import load_env

async def main():

  print("Starting clippy...")
  await load_env()

  # We can't use GlobalEnv here because the container is not yet built
  async with p.Server(session_store=os.getenv("DATABASE_URL"),
                      customer_store=os.getenv("DATABASE_URL"),
                      port=int(os.getenv("APP_PORT")),
                      log_level=LogLevel[os.getenv("LOG_LEVEL", "INFO")]
                      ) as server:
      agent = await server.create_agent(
        name="Son-Vidéo IA",
        description="Tu es un conseiller Son-Vidéo.",
    )

if __name__ == "__main__":
    asyncio.run(main())
